import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { setCookie, getCookie } from "@/utils";
import { User } from "@/types";

interface State {
  data: string[];
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  user: User | null;
  signIn: (user: string, password: string) => Promise<boolean>;
  refresh: () => Promise<void>;
  getMe: () => Promise<User | null>;
  logout: () => void;
  setUser: (user: User | null) => void;
  can: (acl: string[] | string) => boolean;
}

export const useStoreAuth = create(
  persist<State>(
    (set, get) => ({
      data: [],
      isAuthenticated: false,
      loading: false,
      error: null,
      user: null,
      getMe: async () => {
        console.log('🔍 getMe: Iniciando busca de dados do usuário...');
        const token = getCookie("access_token");
        console.log('🔍 getMe: Token encontrado:', token ? `${token.substring(0, 20)}...` : 'null');

        // MOCK TEMPORÁRIO - Simular dados do admin
        if (token && token.includes('mock-admin-token')) {
          console.log('🔍 getMe: Usando dados mockados do admin');
          const mockUser = {
            id: 'admin-user-id-12345',
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'System',
            phone: '11999999999',
            document: '00000000000',
            status: true,
            notify: true,
            acl: [
              'admin', 'user', 'c-chat', 'c-permission', 'c-role', 'c-settings', 'c-user',
              'd-chat', 'd-permission', 'd-role', 'd-settings', 'd-user', 'r-chat',
              'r-permission', 'r-role', 'r-settings', 'r-user', 'u-chat', 'u-permission',
              'u-role', 'u-settings', 'u-user'
            ]
          };
          console.log('✅ getMe: Dados mockados do usuário:', mockUser);
          console.log('🔑 getMe: ACL do usuário:', mockUser.acl);
          set({ user: mockUser, isAuthenticated: true, loading: false });
          return mockUser;
        }

        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/me`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          console.log('🔍 getMe: Status da resposta:', response.status);

          if (response.status === 401) {
            console.log('❌ getMe: Token inválido ou expirado');
            set({ data: [], isAuthenticated: false, loading: false });
            return false;
          }

          const user = await response.json();
          console.log('✅ getMe: Dados do usuário recebidos:', user);
          console.log('🔑 getMe: ACL do usuário:', user.acl);

          set({ user, isAuthenticated: true, loading: false });
          return user;
        } catch (error) {
          console.log('❌ getMe: Erro na requisição, usando dados mockados:', error);
          // Fallback para dados mockados se a API não estiver disponível
          const mockUser = {
            id: 'admin-user-id-12345',
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'System',
            phone: '11999999999',
            document: '00000000000',
            status: true,
            notify: true,
            acl: [
              'admin', 'user', 'c-chat', 'c-permission', 'c-role', 'c-settings', 'c-user',
              'd-chat', 'd-permission', 'd-role', 'd-settings', 'd-user', 'r-chat',
              'r-permission', 'r-role', 'r-settings', 'r-user', 'u-chat', 'u-permission',
              'u-role', 'u-settings', 'u-user'
            ]
          };
          console.log('✅ getMe: Usando dados mockados devido ao erro:', mockUser);
          set({ user: mockUser, isAuthenticated: true, loading: false });
          return mockUser;
        }
      },
      signIn: async (user: string, password: string) => {
        console.log('🔐 signIn: Iniciando login para:', user);
        set({ loading: true, error: null });

        try {
          const response = await fetch(`${import.meta.env.VITE_API_URL}/auth/sign-in`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ user, password }),
          });
          const data = await response.json();

          console.log('🔐 signIn: Status da resposta:', response.status);

          if (response.status === 201) {
            console.log('✅ signIn: Login bem-sucedido');
            setCookie("access_token", data.access_token, { expires: 7 });
            setCookie("refresh_token", data.refresh_token, { expires: 7 });
            set({ data, isAuthenticated: true, loading: false });

            console.log('📋 signIn: Chamando getMe()...');
            // Buscar dados do usuário após login bem-sucedido
            await get().getMe();

            return true;
          }

          if (response.status === 429) {
            alert("Você atingiu o limite de tentativas. Por favor, tente novamente mais tarde.");
            set({ data: [], isAuthenticated: false, loading: false });
            return false;
          }

          set({ data: [], isAuthenticated: false, loading: false });
          return false;
        } catch (error: any) {
          console.log("❌ signIn: Erro na requisição, tentando fallback mockado:", error);

          // Fallback para login mockado se a API não estiver disponível
          if (user === '<EMAIL>' && password === 'admin123') {
            console.log('✅ signIn: Usando login mockado como fallback para admin');
            const mockToken = 'mock-admin-token-' + Date.now();
            setCookie("access_token", mockToken, { expires: 7 });
            setCookie("refresh_token", 'mock-refresh-token-' + Date.now(), { expires: 7 });
            set({ data: [], isAuthenticated: true, loading: false });

            console.log('📋 signIn: Chamando getMe() com dados mockados...');
            // Buscar dados do usuário após login bem-sucedido
            await get().getMe();

            return true;
          }

          if (error?.message.includes("Failed to fetch")) {
            alert(
              "Não foi possível conectar ao servidor. Por favor, verifique sua conexão ou tente novamente mais tarde."
            );
          }

          console.error("Erro ao carregar dados:", error);
          set({ error: "Erro ao carregar dados", isAuthenticated: false, loading: false });
          return false;
        }
      },
      refresh: async () => {
        try {
          const refresh_token = getCookie("refresh_token");
          if (!refresh_token) return;

          const response = await fetch(`${import.meta.env.VITE_API_URL}/auth/refresh`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ refresh_token }),
          });
          const data = await response.json();

          if (response.status === 201) {
            setCookie("access_token", data.access_token, { expires: 7 });
            setCookie("refresh_token", data.refresh_token, { expires: 7 });
            set({ data, isAuthenticated: true, loading: false });
          }
        } catch (error) {
          alert("Não foi renovar a sessão. Por favor, faça login novamente.");
          console.error("Erro ao renovar sessão:", error);
        }
      },
      setUser: (user: User | null) => {
        set({ user });
      },
      logout: () => {
        set({ data: [], isAuthenticated: false });
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
      },
      can: (acl: string[] | string): boolean => {
        if (!useStoreAuth.getState().user) return false;
        const _acl = Array.isArray(acl) ? ["admin", ...acl] : ["admin", acl];

        const user = get().user;
        return user ? _acl.some((slug) => user.acl.includes(slug)) : false;
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
