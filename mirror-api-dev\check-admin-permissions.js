const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAdminPermissions() {
    try {
        // Buscar o usuário admin
        const adminUser = await prisma.user.findUnique({
            where: { email: '<EMAIL>' },
            include: {
                RoleUser: {
                    include: {
                        role: {
                            include: {
                                PermissionRole: {
                                    include: {
                                        permission: true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });

        if (!adminUser) {
            console.log('❌ Usuário admin não encontrado!');
            return;
        }

        console.log('✅ Usuário admin encontrado:');
        console.log(`- ID: ${adminUser.id}`);
        console.log(`- Email: ${adminUser.email}`);
        console.log(`- Nome: ${adminUser.first_name} ${adminUser.last_name}`);
        console.log(`- Status: ${adminUser.status ? 'Ativo' : 'Inativo'}`);
        console.log(`- Role (campo): ${adminUser.role}`);

        console.log('\n📋 Roles associados:');
        if (adminUser.RoleUser.length === 0) {
            console.log('❌ Nenhum role associado ao usuário!');
        } else {
            adminUser.RoleUser.forEach(roleUser => {
                console.log(`- ${roleUser.role.name} (${roleUser.role.slug})`);
                console.log(`  Permissões: ${roleUser.role.PermissionRole.length}`);
            });
        }

        // Simular a consulta que o sistema faz para obter ACL
        const slugs = await prisma.$queryRaw`
        SELECT r.slug as slugs
        FROM role_user ru
        JOIN roles r ON ru.role_id = r.id
        WHERE ru.user_id = ${adminUser.id}
        UNION
        SELECT p.slug as slugs
        FROM role_user ru
        JOIN permission_role pr ON ru.role_id = pr.role_id
        JOIN permissions p ON pr.permission_id = p.id
        WHERE ru.user_id = ${adminUser.id}
        `;

        console.log('\n🔑 ACL calculado pelo sistema:');
        console.log('Slugs encontrados:', slugs.map(s => s.slugs));

        // Verificar se tem permissão de admin
        const hasAdminPermission = slugs.some(s => s.slugs === 'admin');
        console.log(`\n🔐 Tem permissão de admin: ${hasAdminPermission ? '✅ SIM' : '❌ NÃO'}`);

        // Listar todas as permissões disponíveis
        const allPermissions = await prisma.permission.findMany({
            select: { name: true, slug: true, status: true }
        });
        console.log(`\n📝 Total de permissões no sistema: ${allPermissions.length}`);
        
        // Verificar role admin
        const adminRole = await prisma.role.findUnique({
            where: { slug: 'admin' },
            include: {
                PermissionRole: {
                    include: {
                        permission: true
                    }
                }
            }
        });

        if (adminRole) {
            console.log(`\n👑 Role Admin encontrado:`);
            console.log(`- ID: ${adminRole.id}`);
            console.log(`- Nome: ${adminRole.name}`);
            console.log(`- Slug: ${adminRole.slug}`);
            console.log(`- Permissões associadas: ${adminRole.PermissionRole.length}`);
        } else {
            console.log('\n❌ Role Admin não encontrado!');
        }

    } catch (error) {
        console.error('❌ Erro ao verificar permissões:', error);
    } finally {
        await prisma.$disconnect();
    }
}

checkAdminPermissions();
