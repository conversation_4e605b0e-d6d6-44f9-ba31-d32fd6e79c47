const { PrismaClient } = require('@prisma/client');
const fetch = require('node-fetch');

const prisma = new PrismaClient();

async function testAdminLogin() {
    try {
        console.log('🔐 Testando login do admin...');

        // 1. Fazer login
        const loginResponse = await fetch('http://localhost:3333/auth/sign-in', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user: '<EMAIL>',
                password: 'admin123'
            })
        });

        if (loginResponse.status !== 201) {
            console.log('❌ Falha no login:', await loginResponse.text());
            return;
        }

        const loginData = await loginResponse.json();
        console.log('✅ Login realizado com sucesso');
        console.log(`- Access Token: ${loginData.access_token.substring(0, 50)}...`);

        // 2. Testar endpoint /me
        console.log('\n📋 Testando endpoint /me...');
        const meResponse = await fetch('http://localhost:3333/me', {
            headers: {
                'Authorization': `Bearer ${loginData.access_token}`
            }
        });

        if (meResponse.status !== 200) {
            console.log('❌ Falha no endpoint /me:', meResponse.status, await meResponse.text());
            return;
        }

        const userData = await meResponse.json();
        console.log('✅ Endpoint /me funcionando');
        console.log('📊 Dados do usuário:');
        console.log(`- ID: ${userData.id}`);
        console.log(`- Email: ${userData.email}`);
        console.log(`- Nome: ${userData.first_name} ${userData.last_name}`);
        console.log(`- ACL: [${userData.acl.join(', ')}]`);
        console.log(`- Tem permissão admin: ${userData.acl.includes('admin') ? '✅ SIM' : '❌ NÃO'}`);

        // 3. Testar uma rota protegida
        console.log('\n🛡️ Testando rota protegida /users...');
        const usersResponse = await fetch('http://localhost:3333/users', {
            headers: {
                'Authorization': `Bearer ${loginData.access_token}`
            }
        });

        console.log(`- Status da resposta: ${usersResponse.status}`);
        if (usersResponse.status === 200) {
            console.log('✅ Acesso autorizado à rota /users');
        } else {
            console.log('❌ Acesso negado à rota /users:', await usersResponse.text());
        }

    } catch (error) {
        console.error('❌ Erro no teste:', error);
    } finally {
        await prisma.$disconnect();
    }
}

testAdminLogin();
