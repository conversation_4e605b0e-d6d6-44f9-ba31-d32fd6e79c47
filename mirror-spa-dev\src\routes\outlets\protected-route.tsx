import { Navigate, useLocation } from "react-router-dom";
import { useStoreAuth } from "@/states/useStoreAuth";
import { toast } from "sonner";
import { useEffect } from "react"; // Importe o useEffect

interface ProtectedRouteProps {
  children: JSX.Element;
  can: string[];
}

export function ProtectedRoute({ children, can }: ProtectedRouteProps) {
  const location = useLocation();
  const { user } = useStoreAuth();

  console.log('🛡️ ProtectedRoute: Verificando permissões para:', location.pathname);
  console.log('🛡️ ProtectedRoute: Permissões necessárias:', can);
  console.log('🛡️ ProtectedRoute: Usuário atual:', user);
  console.log('🛡️ ProtectedRoute: ACL do usuário:', user?.acl);

  const hasPermission =
    can.length === 0 || ["admin", ...can].some((slug) => user?.acl?.includes(slug));

  console.log('🛡️ ProtectedRoute: Tem permissão?', hasPermission);

  useEffect(() => {
    if (!hasPermission) {
      console.log('❌ ProtectedRoute: Acesso negado para:', location.pathname);
      toast.error("Você não tem permissão para acessar esta página: " + location.pathname);
    }
  }, [hasPermission, location.pathname]);

  if (!hasPermission) {
    return <Navigate to="/" />;
  }

  return children;
}
